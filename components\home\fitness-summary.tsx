import { Text, TouchableOpacity, View } from 'react-native';
import React, { ReactNode } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import moment from 'moment';
import {
  <PERSON><PERSON><PERSON>,
  CheckCircle2,
  Clock,
  Award,
  Flame,
  TrendingUp,
  Target,
  Calendar,
} from 'lucide-react-native';
import { Badge } from '@/components/ui/badge';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { ScrollView } from 'react-native';
import { Button } from '../ui/button';

const FitnessSummary = () => {
  const today = new Date();
  const todayISO = moment(today).format('YYYY-MM-DD');
  const router = useRouter();

  const workoutsToday = useQuery(api.fitness.getWorkouts, {
    startDate: todayISO,
    endDate: todayISO,
  });

  const completedWorkoutsCount = useQuery(
    api.fitness.getCompletedWorkoutsCount,
  );

  const totalWorkoutsToday = workoutsToday?.length || 0;
  const completedToday = workoutsToday?.filter((w) => w.completed).length || 0;

  const progressValue =
    totalWorkoutsToday > 0 ? (completedToday / totalWorkoutsToday) * 100 : 0;

  interface StatCardProps {
    icon: ReactNode;
    title: string;
    value: string;
    color: string;
  }

  const StatCard = ({ icon, title, value, color }: StatCardProps) => (
    <Card className="flex-1 bg-card/80 backdrop-blur-lg shadow-lg">
      <CardContent className="p-4 flex-row items-center gap-2">
        <View
          className={`bg-${color} p-2 rounded-xl w-10 h-10 items-center justify-center`}
        >
          {icon}
        </View>
        <View>
          <Text className="text-sm text-muted-foreground">{title}</Text>
          <Text className="text-xl font-semibold mt-1">{value}</Text>
        </View>
      </CardContent>
    </Card>
  );

  return (
    <View className="space-y-6">
      {/* Hero Card */}
      <View className="bg-primary rounded-lg overflow-hidden border-0 shadow-2xl">
        <LinearGradient
          colors={['#667db6', '#0082c8']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          className="p-6 rounded-lg"
        >
          <View className="flex-row justify-between items-center mb-6">
            <View>
              <Text className="text-white text-2xl font-bold">
                Fitness Overview
              </Text>
              <Text className="text-white/80 mt-1">
                {moment(today).format('dddd, MMMM D')}
              </Text>
            </View>
            <View className="bg-white/20 rounded-full p-3">
              <Dumbbell size={24} color="#fff" />
            </View>
          </View>

          <View className="space-y-4">
            <View className="space-y-2 flex-row gap-2 mb-2">
              <CheckCircle2 size={20} color="#10b981" />
              <Text className="text-white font-medium">
                Today: {completedToday}/{totalWorkoutsToday} Workouts (
                {Math.round(progressValue)}%)
              </Text>
            </View>
            <View className="flex-row items-center gap-2">
              <Progress
                value={progressValue}
                className="h-4"
                indicatorClassName="bg-orange-500"
              />
            </View>
          </View>
        </LinearGradient>
      </View>

      {/* Stats Grid */}
      <View className="flex-row gap-4 mt-4">
        <StatCard
          icon={<TrendingUp size={20} color="#3b82f6" />}
          title="Active Streak"
          value={`${completedWorkoutsCount ?? 0} days`}
          color="blue"
        />

        <StatCard
          icon={<Target size={20} color="#10b981" />}
          title="Completed Today"
          value={`${completedToday}/${totalWorkoutsToday}`}
          color="emerald"
        />
      </View>
      <Button
        onPress={() => router.push('/(tabs)/fitness/schedule')}
        className="my-3"
      >
        <Text className="text-white font-medium">Today's Workouts</Text>
      </Button>
    </View>
  );
};

export default FitnessSummary;
