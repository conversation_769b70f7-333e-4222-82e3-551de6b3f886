export interface DailyCalories {
  breakfast: number;
  lunch: number;
  dinner: number;
  snacks: number;
}

export type HealthPlanType =
  | 'weight'
  | 'heart'
  | 'prenatal'
  | 'sports'
  | 'recovery'
  | 'senior'
  | 'gluten'
  | 'diabetes'
  | 'gut'
  | 'vegan'
  | 'muscle'
  | 'skin'
  | 'detox';

export interface HealthPlanData {
  type: HealthPlanType;
  title: string;
  description: string;
  dailyCalories: DailyCalories;
}

export const healthPlansData: HealthPlanData[] = [
  {
    type: 'weight',
    title: 'Weight Management',
    description:
      'Personalized plan for healthy weight management through balanced nutrition.',
    dailyCalories: { breakfast: 400, lunch: 600, dinner: 600, snacks: 200 },
  },
  {
    type: 'heart',
    title: 'Heart Health',
    description:
      'Focus on cardiovascular health with heart-friendly meal plans.',
    dailyCalories: { breakfast: 350, lunch: 550, dinner: 550, snacks: 150 },
  },
  {
    type: 'prenatal',
    title: 'Prenatal/Pregnancy',
    description: 'Nutritional guidance for a healthy pregnancy journey.',
    dailyCalories: { breakfast: 500, lunch: 700, dinner: 700, snacks: 300 },
  },
  {
    type: 'sports',
    title: 'Sports Nutrition',
    description: 'Optimize your athletic performance with targeted nutrition.',
    dailyCalories: { breakfast: 600, lunch: 800, dinner: 800, snacks: 400 },
  },
  {
    type: 'recovery',
    title: 'Post-Surgery Recovery',
    description: 'Support your recovery with proper nutrition.',
    dailyCalories: { breakfast: 450, lunch: 650, dinner: 650, snacks: 250 },
  },
  {
    type: 'senior',
    title: 'Senior Wellness',
    description: 'Age-appropriate nutrition for healthy aging.',
    dailyCalories: { breakfast: 300, lunch: 500, dinner: 500, snacks: 100 },
  },
  {
    type: 'gluten',
    title: 'Gluten-Free/Celiac',
    description: 'Safe and nutritious gluten-free meal planning.',
    dailyCalories: { breakfast: 400, lunch: 600, dinner: 600, snacks: 200 },
  },
  {
    type: 'diabetes',
    title: 'Diabetes Management',
    description: 'Dietary plans to help manage blood sugar levels.',
    dailyCalories: { breakfast: 350, lunch: 500, dinner: 500, snacks: 150 },
  },
  {
    type: 'gut',
    title: 'Gut Health',
    description: 'Nutrition focused on improving digestive health.',
    dailyCalories: { breakfast: 400, lunch: 550, dinner: 550, snacks: 200 },
  },
  {
    type: 'vegan',
    title: 'Vegan Nutrition',
    description: 'Comprehensive plant-based meal planning.',
    dailyCalories: { breakfast: 450, lunch: 600, dinner: 600, snacks: 250 },
  },
  {
    type: 'muscle',
    title: 'Muscle Gain',
    description: 'High-protein plans for muscle growth and recovery.',
    dailyCalories: { breakfast: 700, lunch: 900, dinner: 900, snacks: 500 },
  },
  {
    type: 'skin',
    title: 'Skin Enhancement',
    description: 'Nutritional plan to promote healthy and radiant skin.',
    dailyCalories: { breakfast: 300, lunch: 450, dinner: 450, snacks: 100 },
  },
  {
    type: 'detox',
    title: 'Detox & Cleanse',
    description: 'A plan focused on cleansing and detoxifying the body.',
    dailyCalories: { breakfast: 250, lunch: 400, dinner: 400, snacks: 100 },
  },
];
