export interface DailyCalories {
  breakfast: number;
  lunch: number;
  dinner: number;
  snacks: number;
}

export type HealthPlanType =
  | 'weight'
  | 'heart'
  | 'prenatal'
  | 'sports'
  | 'recovery'
  | 'senior'
  | 'gluten'
  | 'diabetes'
  | 'gut'
  | 'vegan'
  | 'muscle'
  | 'skin'
  | 'detox'
  | 'mediterranean'
  | 'keto'
  | 'intermittent'
  | 'dash'
  | 'paleo';

export interface HealthPlanData {
  type: HealthPlanType;
  title: string;
  description: string;
  dailyCalories: DailyCalories;
}

export const healthPlansData: HealthPlanData[] = [
  {
    type: 'weight',
    title: 'Weight Management',
    description:
      'Personalized plan for healthy weight management through balanced nutrition and calorie deficit.',
    dailyCalories: { breakfast: 350, lunch: 500, dinner: 600, snacks: 150 }, // ~1600 kcal
  },
  {
    type: 'heart',
    title: 'Heart Health',
    description:
      'Focus on cardiovascular health with heart-friendly, low sodium and low saturated fat meal plans.',
    dailyCalories: { breakfast: 400, lunch: 600, dinner: 700, snacks: 200 }, // ~1900 kcal
  },
  {
    type: 'prenatal',
    title: 'Prenatal/Pregnancy',
    description:
      'Nutritional guidance for a healthy pregnancy journey with increased caloric needs.',
    dailyCalories: { breakfast: 500, lunch: 700, dinner: 900, snacks: 300 }, // ~2400 kcal
  },
  {
    type: 'sports',
    title: 'Sports Nutrition',
    description:
      'Optimize your athletic performance with high-energy, protein-rich nutrition.',
    dailyCalories: { breakfast: 700, lunch: 900, dinner: 1000, snacks: 400 }, // ~3000 kcal
  },
  {
    type: 'recovery',
    title: 'Post-Surgery Recovery',
    description:
      'Support your recovery with protein-rich, nutrient-dense meals.',
    dailyCalories: { breakfast: 500, lunch: 700, dinner: 800, snacks: 200 }, // ~2200 kcal
  },
  {
    type: 'senior',
    title: 'Senior Wellness',
    description:
      'Age-appropriate nutrition for healthy aging, focusing on nutrient density.',
    dailyCalories: { breakfast: 350, lunch: 500, dinner: 600, snacks: 150 }, // ~1600 kcal
  },
  {
    type: 'gluten',
    title: 'Gluten-Free/Celiac',
    description:
      'Safe and nutritious gluten-free meal planning for celiac disease or gluten sensitivity.',
    dailyCalories: { breakfast: 400, lunch: 600, dinner: 800, snacks: 200 }, // ~2000 kcal
  },
  {
    type: 'diabetes',
    title: 'Diabetes Management',
    description:
      'Dietary plans to help manage blood sugar levels with controlled carbohydrates.',
    dailyCalories: { breakfast: 350, lunch: 600, dinner: 700, snacks: 150 }, // ~1800 kcal
  },
  {
    type: 'gut',
    title: 'Gut Health',
    description:
      'Nutrition focused on improving digestive health with fiber-rich foods.',
    dailyCalories: { breakfast: 400, lunch: 600, dinner: 800, snacks: 200 }, // ~2000 kcal
  },
  {
    type: 'vegan',
    title: 'Vegan Nutrition',
    description:
      'Comprehensive plant-based meal planning for all essential nutrients.',
    dailyCalories: { breakfast: 400, lunch: 700, dinner: 800, snacks: 200 }, // ~2100 kcal
  },
  {
    type: 'muscle',
    title: 'Muscle Gain',
    description:
      'High-protein, high-calorie plans for muscle growth and recovery.',
    dailyCalories: { breakfast: 800, lunch: 1000, dinner: 1100, snacks: 400 }, // ~3300 kcal
  },
  {
    type: 'skin',
    title: 'Skin Enhancement',
    description:
      'Nutritional plan to promote healthy and radiant skin with antioxidants.',
    dailyCalories: { breakfast: 350, lunch: 600, dinner: 700, snacks: 150 }, // ~1800 kcal
  },
  {
    type: 'detox',
    title: 'Detox & Cleanse',
    description:
      'A short-term plan focused on cleansing and detoxifying the body.',
    dailyCalories: { breakfast: 300, lunch: 500, dinner: 600, snacks: 100 }, // ~1500 kcal
  },
  {
    type: 'mediterranean' as HealthPlanType,
    title: 'Mediterranean Diet',
    description:
      'Heart-healthy, balanced diet inspired by Mediterranean cuisine.',
    dailyCalories: { breakfast: 400, lunch: 700, dinner: 900, snacks: 200 }, // ~2200 kcal
  },
  {
    type: 'keto' as HealthPlanType,
    title: 'Keto Diet',
    description:
      'Low-carb, high-fat ketogenic diet for weight loss and metabolic health.',
    dailyCalories: { breakfast: 400, lunch: 600, dinner: 800, snacks: 200 }, // ~2000 kcal
  },
  {
    type: 'intermittent' as HealthPlanType,
    title: 'Intermittent Fasting',
    description: '16:8 fasting pattern, calories consumed in an 8-hour window.',
    dailyCalories: { breakfast: 0, lunch: 800, dinner: 900, snacks: 300 }, // ~2000 kcal
  },
  {
    type: 'dash' as HealthPlanType,
    title: 'DASH Diet',
    description:
      'Dietary Approaches to Stop Hypertension, rich in fruits and vegetables.',
    dailyCalories: { breakfast: 400, lunch: 700, dinner: 900, snacks: 200 }, // ~2200 kcal
  },
  {
    type: 'paleo' as HealthPlanType,
    title: 'Paleo Diet',
    description: 'Whole foods, unprocessed, high-protein paleo meal plan.',
    dailyCalories: { breakfast: 400, lunch: 700, dinner: 900, snacks: 200 }, // ~2200 kcal
  },
];
