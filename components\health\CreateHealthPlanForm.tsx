import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { theme } from '@/constants/theme';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Button } from '../ui/button';
import { Card, CardContent, CardTitle } from '../ui/card';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Text } from '../ui/text';
import { Label } from '../ui/label';
import { HealthPlanType, healthPlansData } from '@/data/healthPlansData';

interface CreateHealthPlanFormProps {
  onPlanCreated: () => void;
  onCancel: () => void;
}

export function CreateHealthPlanForm({
  onPlanCreated,
  onCancel,
}: CreateHealthPlanFormProps) {
  const createHealthPlanMutation = useMutation(
    api.healthPlans.createHealthPlan,
  );

  const [planType, setPlanType] = useState<HealthPlanType>('weight');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  const handleCreatePlan = async () => {
    try {
      const selectedPlanData = healthPlansData.find(
        (plan) => plan.type === planType,
      );

      if (!selectedPlanData) {
        console.error('Selected plan type not found:', planType);
        // TODO: Show error message to user
        return;
      }

      await createHealthPlanMutation({
        isActive: true, // Automatically activate the new plan
        dailyCalories: selectedPlanData.dailyCalories,
        type: planType,
        startDate: startDate.toISOString(),
        endDate: endDate?.toISOString(),
      });
      onPlanCreated();
    } catch (error) {
      console.error('Failed to create health plan:', error);
      // TODO: Show error message to user
    }
  };

  const onStartDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || startDate;
    setShowStartDatePicker(false);
    setStartDate(currentDate);
  };

  const onEndDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || endDate;
    setShowEndDatePicker(false);
    setEndDate(currentDate);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Create New Health Plan</Text>

      <Card style={styles.card}>
        <CardTitle>Plan Type</CardTitle>
        <CardContent>
          <RadioGroup
            onValueChange={(newValue) =>
              setPlanType(newValue as HealthPlanType)
            }
            value={planType}
          >
            {healthPlansData.map((plan) => (
              <View key={plan.type} style={styles.radioOption}>
                <RadioGroupItem value={plan.type} />
                <Label>{plan.title}</Label>
              </View>
            ))}
          </RadioGroup>
        </CardContent>
      </Card>

      <Card style={styles.card}>
        <CardTitle>Start Date</CardTitle>
        <CardContent>
          <Button onPress={() => setShowStartDatePicker(true)}>
            <Text>{startDate.toDateString()}</Text>
          </Button>
          {showStartDatePicker && (
            <DateTimePicker
              value={startDate}
              mode="date"
              display="default"
              onChange={onStartDateChange}
            />
          )}
        </CardContent>
      </Card>

      <Card style={styles.card}>
        <CardTitle>End Date (Optional)</CardTitle>
        <CardContent>
          <Button onPress={() => setShowEndDatePicker(true)}>
            <Text>{endDate ? endDate.toDateString() : 'Select End Date'}</Text>
          </Button>
          {showEndDatePicker && (
            <DateTimePicker
              value={endDate || new Date()}
              mode="date"
              display="default"
              onChange={onEndDateChange}
            />
          )}
        </CardContent>
      </Card>

      <View style={styles.buttonContainer}>
        <Button variant="outline" onPress={onCancel} style={styles.button}>
          <Text>Cancel</Text>
        </Button>
        <Button onPress={handleCreatePlan} style={styles.button}>
          <Text>Create Plan</Text>
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.l,
    backgroundColor: theme.colors.gray[50],
  },
  title: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.gray[900],
    textAlign: 'center',
    marginBottom: theme.spacing.l,
  },
  card: {
    marginBottom: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
    ...theme.shadows.small,
  },
  input: {
    borderWidth: 1,
    borderColor: theme.colors.gray[300],
    borderRadius: theme.borderRadius.s,
    padding: theme.spacing.s,
    marginBottom: theme.spacing.s,
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: theme.spacing.l,
    marginBottom: theme.spacing.xl,
  },
  button: {
    flex: 1,
    marginHorizontal: theme.spacing.s,
  },
});
