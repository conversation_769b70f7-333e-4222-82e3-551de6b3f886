import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { theme } from '@/constants/theme';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Button } from '../ui/button';
import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
  CardHeader,
} from '../ui/card';
import { Text } from '../ui/text';
import { HealthPlanType, healthPlansData } from '@/data/healthPlansData';

interface CreateHealthPlanFormProps {
  onPlanCreated: () => void;
  onCancel: () => void;
}

export function CreateHealthPlanForm({
  onPlanCreated,
  onCancel,
}: CreateHealthPlanFormProps) {
  const createHealthPlanMutation = useMutation(
    api.healthPlans.createHealthPlan,
  );

  const [planType, setPlanType] = useState<HealthPlanType>('weight');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  const handleCreatePlan = async () => {
    try {
      const selectedPlanData = healthPlansData.find(
        (plan) => plan.type === planType,
      );

      if (!selectedPlanData) {
        console.error('Selected plan type not found:', planType);
        // TODO: Show error message to user
        return;
      }

      await createHealthPlanMutation({
        isActive: true, // Automatically activate the new plan
        dailyCalories: selectedPlanData.dailyCalories,
        type: planType,
        startDate: startDate.toISOString(),
        endDate: endDate?.toISOString(),
      });
      onPlanCreated();
    } catch (error) {
      console.error('Failed to create health plan:', error);
      // TODO: Show error message to user
    }
  };

  const onStartDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || startDate;
    setShowStartDatePicker(false);
    setStartDate(currentDate);
  };

  const onEndDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || endDate;
    setShowEndDatePicker(false);
    setEndDate(currentDate);
  };

  const selectedPlan = healthPlansData.find((plan) => plan.type === planType);
  const totalCalories = selectedPlan
    ? selectedPlan.dailyCalories.breakfast +
      selectedPlan.dailyCalories.lunch +
      selectedPlan.dailyCalories.dinner +
      selectedPlan.dailyCalories.snacks
    : 0;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Create Health Plan</Text>
        <Text style={styles.subtitle}>
          Choose a plan that aligns with your health goals
        </Text>
      </View>

      {/* Plan Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Select Your Plan</Text>
        <View style={styles.planGrid}>
          {healthPlansData.map((plan) => (
            <TouchableOpacity
              key={plan.type}
              style={[
                styles.planCard,
                planType === plan.type && styles.planCardSelected,
              ]}
              onPress={() => setPlanType(plan.type)}
              activeOpacity={0.7}
            >
              <View style={styles.planCardHeader}>
                <Text
                  style={[
                    styles.planTitle,
                    planType === plan.type && styles.planTitleSelected,
                  ]}
                >
                  {plan.title}
                </Text>
                <View
                  style={[
                    styles.selectionIndicator,
                    planType === plan.type && styles.selectionIndicatorSelected,
                  ]}
                />
              </View>
              <Text style={styles.planDescription}>{plan.description}</Text>
              <View style={styles.calorieBreakdown}>
                <Text style={styles.calorieLabel}>Daily Calories</Text>
                <View style={styles.calorieRow}>
                  <Text style={styles.calorieItem}>
                    B: {plan.dailyCalories.breakfast}
                  </Text>
                  <Text style={styles.calorieItem}>
                    L: {plan.dailyCalories.lunch}
                  </Text>
                  <Text style={styles.calorieItem}>
                    D: {plan.dailyCalories.dinner}
                  </Text>
                  <Text style={styles.calorieItem}>
                    S: {plan.dailyCalories.snacks}
                  </Text>
                </View>
                <Text style={styles.totalCalories}>
                  Total:{' '}
                  {plan.dailyCalories.breakfast +
                    plan.dailyCalories.lunch +
                    plan.dailyCalories.dinner +
                    plan.dailyCalories.snacks}{' '}
                  cal
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Selected Plan Summary */}
      {selectedPlan && (
        <Card style={styles.summaryCard}>
          <CardHeader>
            <CardTitle style={styles.summaryTitle}>Plan Summary</CardTitle>
            <CardDescription>
              {selectedPlan.title} • {totalCalories} calories daily
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* Date Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Plan Duration</Text>

        <View style={styles.dateContainer}>
          <TouchableOpacity
            style={styles.dateCard}
            onPress={() => setShowStartDatePicker(true)}
            activeOpacity={0.7}
          >
            <Text style={styles.dateLabel}>Start Date</Text>
            <Text style={styles.dateValue}>{formatDate(startDate)}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.dateCard}
            onPress={() => setShowEndDatePicker(true)}
            activeOpacity={0.7}
          >
            <Text style={styles.dateLabel}>End Date</Text>
            <Text style={styles.dateValue}>
              {endDate ? formatDate(endDate) : 'Optional'}
            </Text>
          </TouchableOpacity>
        </View>

        {showStartDatePicker && (
          <DateTimePicker
            value={startDate}
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={onStartDateChange}
            minimumDate={new Date()}
          />
        )}

        {showEndDatePicker && (
          <DateTimePicker
            value={endDate || new Date()}
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={onEndDateChange}
            minimumDate={startDate}
          />
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <Button
          variant="outline"
          onPress={onCancel}
          style={styles.cancelButton}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </Button>
        <Button onPress={handleCreatePlan} style={styles.createButton}>
          <Text style={styles.createButtonText}>Create Plan</Text>
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
    paddingHorizontal: theme.spacing.l,
  },
  header: {
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.l,
    alignItems: 'center',
  },
  title: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxxl,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
    textAlign: 'center',
    lineHeight: 22,
  },
  section: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.m,
  },
  planGrid: {
    gap: theme.spacing.m,
  },
  planCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.m,
    padding: theme.spacing.l,
    borderWidth: 2,
    borderColor: theme.colors.gray[200],
    ...theme.shadows.small,
  },
  planCardSelected: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primaryLight,
  },
  planCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.s,
  },
  planTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    flex: 1,
  },
  planTitleSelected: {
    color: theme.colors.primary,
  },
  selectionIndicator: {
    width: 20,
    height: 20,
    borderRadius: theme.borderRadius.round,
    borderWidth: 2,
    borderColor: theme.colors.gray[300],
    backgroundColor: theme.colors.white,
  },
  selectionIndicatorSelected: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary,
  },
  planDescription: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    lineHeight: 18,
    marginBottom: theme.spacing.m,
  },
  calorieBreakdown: {
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.s,
    padding: theme.spacing.s,
  },
  calorieLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.gray[700],
    marginBottom: theme.spacing.xs,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  calorieRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.xs,
  },
  calorieItem: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[800],
  },
  totalCalories: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.primary,
    textAlign: 'center',
  },
  summaryCard: {
    marginBottom: theme.spacing.l,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.m,
    ...theme.shadows.medium,
  },
  summaryTitle: {
    color: theme.colors.white,
    fontFamily: theme.typography.fontFamily.semiBold,
  },
  dateContainer: {
    flexDirection: 'row',
    gap: theme.spacing.m,
  },
  dateCard: {
    flex: 1,
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.m,
    padding: theme.spacing.l,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
    alignItems: 'center',
  },
  dateLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.xs,
  },
  dateValue: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
    textAlign: 'center',
  },
  actionContainer: {
    flexDirection: 'row',
    gap: theme.spacing.m,
    paddingBottom: theme.spacing.xl,
    marginTop: theme.spacing.l,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: theme.colors.gray[300],
    borderRadius: theme.borderRadius.m,
    paddingVertical: theme.spacing.m,
  },
  cancelButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[700],
  },
  createButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.m,
    paddingVertical: theme.spacing.m,
    ...theme.shadows.small,
  },
  createButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
  },
});
