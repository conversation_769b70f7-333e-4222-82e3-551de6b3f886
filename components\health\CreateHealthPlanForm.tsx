import React, { useState } from 'react';
import { View, ScrollView, TouchableOpacity, Platform } from 'react-native';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Button } from '../ui/button';
import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
  CardHeader,
} from '../ui/card';
import { Text } from '../ui/text';
import { HealthPlanType, healthPlansData } from '@/data/healthPlansData';
import { cn } from '@/lib/utils';

interface CreateHealthPlanFormProps {
  onPlanCreated: () => void;
  onCancel: () => void;
}

// Helper function to get calorie colors
const getCalorieColor = (
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snacks',
) => {
  const colors = {
    breakfast: 'text-orange-500',
    lunch: 'text-green-500',
    dinner: 'text-blue-500',
    snacks: 'text-purple-500',
  };
  return colors[mealType];
};

export function CreateHealthPlanForm({
  onPlanCreated,
  onCancel,
}: CreateHealthPlanFormProps) {
  const createHealthPlanMutation = useMutation(
    api.healthPlans.createHealthPlan,
  );

  const [planType, setPlanType] = useState<HealthPlanType>('weight');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  const handleCreatePlan = async () => {
    try {
      const selectedPlanData = healthPlansData.find(
        (plan) => plan.type === planType,
      );

      if (!selectedPlanData) {
        console.error('Selected plan type not found:', planType);
        // TODO: Show error message to user
        return;
      }

      await createHealthPlanMutation({
        isActive: true, // Automatically activate the new plan
        dailyCalories: selectedPlanData.dailyCalories,
        type: planType,
        startDate: startDate.toISOString(),
        endDate: endDate?.toISOString(),
      });
      onPlanCreated();
    } catch (error) {
      console.error('Failed to create health plan:', error);
      // TODO: Show error message to user
    }
  };

  const onStartDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || startDate;
    setShowStartDatePicker(false);
    setStartDate(currentDate);
  };

  const onEndDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || endDate;
    setShowEndDatePicker(false);
    setEndDate(currentDate);
  };

  const selectedPlan = healthPlansData.find((plan) => plan.type === planType);
  const totalCalories = selectedPlan
    ? selectedPlan.dailyCalories.breakfast +
      selectedPlan.dailyCalories.lunch +
      selectedPlan.dailyCalories.dinner +
      selectedPlan.dailyCalories.snacks
    : 0;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <ScrollView
      className="flex-1 bg-white px-4"
      showsVerticalScrollIndicator={false}
    >
      {/* Header */}
      <View className="pt-6 pb-8 items-center">
        <Text className="font-poppins-bold text-3xl text-gray-900 mb-1">
          Create Health Plan
        </Text>
        <Text className="font-poppins-regular text-sm text-gray-600 text-center leading-5">
          Choose a plan that aligns with your health goals
        </Text>
      </View>

      {/* Plan Selection */}
      <View className="mb-6">
        <Text className="font-poppins-semibold text-lg text-gray-900 mb-3">
          Select Your Plan
        </Text>
        <View className="gap-3">
          {healthPlansData.map((plan) => (
            <TouchableOpacity
              key={plan.type}
              className={cn(
                'bg-white rounded-lg p-4 border-2 shadow-sm',
                planType === plan.type
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-200',
              )}
              onPress={() => setPlanType(plan.type)}
              activeOpacity={0.7}
            >
              <View className="flex-row justify-between items-center mb-2">
                <Text
                  className={cn(
                    'font-poppins-semibold text-lg flex-1',
                    planType === plan.type ? 'text-primary' : 'text-gray-900',
                  )}
                >
                  {plan.title}
                </Text>
                <View
                  className={cn(
                    'w-5 h-5 rounded-full border-2',
                    planType === plan.type
                      ? 'border-primary bg-primary'
                      : 'border-gray-300 bg-white',
                  )}
                />
              </View>
              <Text className="font-poppins-regular text-sm text-gray-600 leading-4 mb-3">
                {plan.description}
              </Text>
              <View className="bg-gray-50 rounded p-2">
                <Text className="font-poppins-medium text-xs text-gray-700 mb-1 uppercase tracking-wide">
                  Daily Calories
                </Text>
                <View className="flex-row justify-between mb-1">
                  <Text
                    className={cn(
                      'font-poppins-medium text-sm',
                      getCalorieColor('breakfast'),
                    )}
                  >
                    B: {plan.dailyCalories.breakfast}
                  </Text>
                  <Text
                    className={cn(
                      'font-poppins-medium text-sm',
                      getCalorieColor('lunch'),
                    )}
                  >
                    L: {plan.dailyCalories.lunch}
                  </Text>
                  <Text
                    className={cn(
                      'font-poppins-medium text-sm',
                      getCalorieColor('dinner'),
                    )}
                  >
                    D: {plan.dailyCalories.dinner}
                  </Text>
                  <Text
                    className={cn(
                      'font-poppins-medium text-sm',
                      getCalorieColor('snacks'),
                    )}
                  >
                    S: {plan.dailyCalories.snacks}
                  </Text>
                </View>
                <Text className="font-poppins-semibold text-sm text-primary text-center">
                  Total:{' '}
                  {plan.dailyCalories.breakfast +
                    plan.dailyCalories.lunch +
                    plan.dailyCalories.dinner +
                    plan.dailyCalories.snacks}{' '}
                  cal
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Selected Plan Summary */}
      {selectedPlan && (
        <Card className="mb-4 bg-primary rounded-lg shadow-md">
          <CardHeader>
            <CardTitle className="text-white font-poppins-semibold">
              Plan Summary
            </CardTitle>
            <CardDescription className="text-white/90">
              {selectedPlan.title} • {totalCalories} calories daily
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* Date Selection */}
      <View className="mb-6">
        <Text className="font-poppins-semibold text-lg text-gray-900 mb-3">
          Plan Duration
        </Text>

        <View className="flex-row gap-3">
          <TouchableOpacity
            className="flex-1 bg-gray-50 rounded-lg p-4 border border-gray-200 items-center"
            onPress={() => setShowStartDatePicker(true)}
            activeOpacity={0.7}
          >
            <Text className="font-poppins-medium text-sm text-gray-600 mb-1">
              Start Date
            </Text>
            <Text className="font-poppins-semibold text-sm text-gray-900 text-center">
              {formatDate(startDate)}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-1 bg-gray-50 rounded-lg p-4 border border-gray-200 items-center"
            onPress={() => setShowEndDatePicker(true)}
            activeOpacity={0.7}
          >
            <Text className="font-poppins-medium text-sm text-gray-600 mb-1">
              End Date
            </Text>
            <Text className="font-poppins-semibold text-sm text-gray-900 text-center">
              {endDate ? formatDate(endDate) : 'Optional'}
            </Text>
          </TouchableOpacity>
        </View>

        {showStartDatePicker && (
          <DateTimePicker
            value={startDate}
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={onStartDateChange}
            minimumDate={new Date()}
          />
        )}

        {showEndDatePicker && (
          <DateTimePicker
            value={endDate || new Date()}
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={onEndDateChange}
            minimumDate={startDate}
          />
        )}
      </View>

      {/* Action Buttons */}
      <View className="flex-row gap-3 pb-6 mt-4">
        <Button
          variant="outline"
          onPress={onCancel}
          className="flex-1 bg-transparent border-2 border-gray-300 rounded-lg py-3"
        >
          <Text className="font-poppins-semibold text-sm text-gray-700">
            Cancel
          </Text>
        </Button>
        <Button
          onPress={handleCreatePlan}
          className="flex-1 bg-primary rounded-lg py-3 shadow-sm"
        >
          <Text className="font-poppins-semibold text-sm text-white">
            Create Plan
          </Text>
        </Button>
      </View>
    </ScrollView>
  );
}
