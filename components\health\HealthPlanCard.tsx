import React, { useState } from 'react';
import { TouchableOpacity, ScrollView, View } from 'react-native';
import {
  <PERSON>,
  Heart,
  Baby,
  Dumbbell,
  Stethoscope,
  UserCog,
  Wheat,
  Activity,
  Syringe, // For Diabetes
  Leaf, // For Vegan
  Bone, // For Muscle Gain
  Soup, // For Gut Health
  Sparkles, // For Skin Enhancement
  Droplet,
  Weight, // For Detox & Cleanse
} from 'lucide-react-native';
import { Doc } from '@/convex/_generated/dataModel';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { CreateHealthPlanForm } from './CreateHealthPlanForm';
import { Button } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '../ui/dialog';
import { Text } from '../ui/text';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '../ui/card';
import { iconWithClassName } from '@/lib/icons/iconWithClassName';
import { healthPlansData, HealthPlanType } from '@/data/healthPlansData';
import { router } from 'expo-router';

type HealthPlan = Doc<'healthPlan'>;

// Apply cssInterop to each icon to enable className prop
iconWithClassName(Scale);
iconWithClassName(Heart);
iconWithClassName(Baby);
iconWithClassName(Dumbbell);
iconWithClassName(Stethoscope);
iconWithClassName(UserCog);
iconWithClassName(Wheat);
iconWithClassName(Activity);
iconWithClassName(Syringe);
iconWithClassName(Leaf);
iconWithClassName(Bone);
iconWithClassName(Soup);
iconWithClassName(Sparkles);
iconWithClassName(Droplet);

const getPlanDetails = (type: HealthPlanType) => {
  const plan = healthPlansData.find((p) => p.type === type);
  if (plan) {
    let iconComponent;
    switch (type) {
      case 'weight':
        iconComponent = <Scale size={24} className="text-primary" />;
        break;
      case 'heart':
        iconComponent = <Heart size={24} className="text-primary" />;
        break;
      case 'prenatal':
        iconComponent = <Baby size={24} className="text-primary" />;
        break;
      case 'sports':
        iconComponent = <Dumbbell size={24} className="text-primary" />;
        break;
      case 'recovery':
        iconComponent = <Stethoscope size={24} className="text-primary" />;
        break;
      case 'senior':
        iconComponent = <UserCog size={24} className="text-primary" />;
        break;
      case 'gluten':
        iconComponent = <Wheat size={24} className="text-primary" />;
        break;
      case 'diabetes':
        iconComponent = <Syringe size={24} className="text-primary" />;
        break;
      case 'gut':
        iconComponent = <Soup size={24} className="text-primary" />;
        break;
      case 'vegan':
        iconComponent = <Leaf size={24} className="text-primary" />;
        break;
      case 'muscle':
        iconComponent = <Bone size={24} className="text-primary" />;
        break;
      case 'skin':
        iconComponent = <Sparkles size={24} className="text-primary" />;
        break;
      case 'detox':
        iconComponent = <Droplet size={24} className="text-primary" />;
        break;
      default:
        iconComponent = null;
    }
    return {
      title: plan.title,
      description: plan.description,
      icon: iconComponent,
    };
  }
  return {
    title: 'Unknown Plan',
    description: 'No description available for this plan type.',
    icon: null,
  };
};

export function HealthPlanCard({
  activePlan,
}: {
  activePlan: HealthPlan | undefined;
}) {
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [isCreatingNewPlan, setIsCreatingNewPlan] = useState(false);

  const allHealthPlans = useQuery(api.healthPlans.getHealthPlans);
  const userProfile = useQuery(api.userProfile.getProfile);
  const today = new Date();
  const todayISOString = today.toISOString().split('T')[0];

  const mealSummary = useQuery(
    api.meals.getMealsSummary,
    activePlan
      ? {
          startDate: activePlan.startDate.split('T')[0],
          endDate: todayISOString,
        }
      : {
          startDate: todayISOString,
          endDate: todayISOString,
        },
  );

  const updateHealthPlanMutation = useMutation(
    api.healthPlans.updateHealthPlan,
  );
  const createHealthPlanMutation = useMutation(
    api.healthPlans.createHealthPlan,
  );

  const toggleBottomSheet = () => {
    setBottomSheetVisible(!isBottomSheetVisible);
    setIsCreatingNewPlan(false); // Reset create plan state when closing bottom sheet
  };

  const selectPlan = async (plan: HealthPlan) => {
    if (activePlan?._id === plan._id) {
      setBottomSheetVisible(false);
      return;
    }
    await updateHealthPlanMutation({
      id: plan._id,
      updates: { isActive: true },
    });
    setBottomSheetVisible(false);
  };

  const handleCreateNewPlan = () => {
    setIsCreatingNewPlan(true);
  };

  const handlePlanCreated = () => {
    setIsCreatingNewPlan(false);
    setBottomSheetVisible(false);
    // The useQuery will automatically refetch, so no manual refresh needed
  };

  const handleCancelCreatePlan = () => {
    setIsCreatingNewPlan(false);
  };

  const planDetails = activePlan ? getPlanDetails(activePlan.type) : null;

  const daysActive = activePlan
    ? Math.max(
        1,
        Math.ceil(
          (today.getTime() - new Date(activePlan._creationTime).getTime()) /
            (1000 * 60 * 60 * 24),
        ),
      )
    : 0;

  const totalDailyCalorieGoal = activePlan
    ? activePlan.dailyCalories.breakfast +
      activePlan.dailyCalories.lunch +
      activePlan.dailyCalories.dinner +
      activePlan.dailyCalories.snacks
    : 0;

  const totalExpectedCalories = daysActive * totalDailyCalorieGoal;

  const consumedCalories = mealSummary?.totalCalories ?? 0;

  const planAdherence =
    totalExpectedCalories > 0
      ? Math.round((consumedCalories / totalExpectedCalories) * 100)
      : 0;

  return (
    <View className="mx-4 mt-4">
      {activePlan && planDetails ? (
        <Card className="rounded-lg p-4 shadow-md bg-card">
          <CardContent className="p-0">
            <View className="flex-row items-center mb-4">
              <View className="w-12 h-12 rounded-full bg-secondary justify-center items-center shadow-sm">
                {planDetails.icon}
              </View>
              <View className="flex-1 ml-4">
                <Text className="font-bold text-lg text-foreground mb-1">
                  {planDetails.title}
                </Text>
                <Text className="font-normal text-sm text-muted-foreground">
                  {planDetails.description}
                </Text>
              </View>
            </View>
            <View className="flex-row justify-around pt-4 border-t border-border mb-4">
              <View className="items-center">
                <Activity size={16} color="green" />
                <Text className="font-bold text-lg text-foreground my-1">
                  {planAdherence}%
                </Text>
                <Text className="font-normal text-xs text-muted-foreground">
                  Plan Adherence
                </Text>
              </View>
              <View className="w-px h-full bg-border" />
              <View className="items-center">
                <Heart size={16} color={'indigo'} />
                <Text className="font-bold text-lg text-foreground my-1">
                  {daysActive}
                </Text>
                <Text className="font-normal text-xs text-muted-foreground">
                  Days Active
                </Text>
              </View>
              <View className="w-px h-full bg-border" />
              <View className="items-center">
                <Weight size={16} color="orange" className="text-primary" />
                <Text className="font-bold text-lg text-foreground my-1">
                  {userProfile === undefined
                    ? '...'
                    : userProfile?.weight || 'N/A'}
                </Text>
                <Text className="font-normal text-xs text-muted-foreground">
                  Body weight
                </Text>
              </View>
            </View>
            <Button
              onPress={() => router.push('/health/edit-plan')}
              className="w-full"
            >
              <Text>Edit Plan</Text>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Button onPress={toggleBottomSheet}>
          <Text className="font-semibold text-lg text-primary">
            Create Health Plan
          </Text>
        </Button>
      )}

      <Dialog open={isBottomSheetVisible} onOpenChange={setBottomSheetVisible}>
        <DialogContent className="max-w-md">
          {isCreatingNewPlan ? (
            <CreateHealthPlanForm
              onPlanCreated={handlePlanCreated}
              onCancel={handleCancelCreatePlan}
            />
          ) : (
            <>
              <DialogHeader>
                <DialogTitle>Select Health Plan</DialogTitle>
                <DialogDescription>
                  Choose an existing plan or create a new one.
                </DialogDescription>
              </DialogHeader>
              <ScrollView className="max-h-[500px]">
                {allHealthPlans && allHealthPlans.length > 0 ? (
                  allHealthPlans.map((plan) => {
                    const details = getPlanDetails(plan.type);
                    return (
                      <TouchableOpacity
                        key={plan._id}
                        className={cn(
                          'flex-row items-center p-3 rounded-md mb-2 bg-secondary',
                          activePlan?._id === plan._id && 'bg-primary/10',
                        )}
                        onPress={() => selectPlan(plan)}
                      >
                        <View className="w-12 h-12 rounded-full bg-card justify-center items-center shadow-sm">
                          {details.icon}
                        </View>
                        <View className="flex-1 ml-4">
                          <Text className="font-semibold text-base text-foreground mb-1">
                            {details.title}
                          </Text>
                          <Text className="font-normal text-sm text-muted-foreground">
                            {details.description}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    );
                  })
                ) : (
                  <Text>No health plans available. Create one!</Text>
                )}
              </ScrollView>
              <DialogFooter>
                <Button onPress={handleCreateNewPlan} className="mt-4">
                  Create New Plan
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </View>
  );
}
