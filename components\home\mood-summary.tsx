import { View, Animated, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useRef, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Card } from '@/components/ui/card';
import { Text } from '@/components/ui/text';
import { SkeletonLayout } from '../ui/Skeleton';
import {
  Smile,
  Frown,
  Meh,
  Zap,
  Cloud,
  Heart,
  Moon,
  Leaf,
  SmilePlus,
} from 'lucide-react-native';
import moment from 'moment';

interface MoodSummaryProps {
  onLogMoodPress: () => void;
}

const MoodSummary = ({ onLogMoodPress }: MoodSummaryProps) => {
  const currentMood = useQuery(api.mood.getCurrentMood);
  const iconScale = useRef(new Animated.Value(0.92)).current;

  useEffect(() => {
    Animated.spring(iconScale, {
      toValue: 1,
      useNativeDriver: true,
      friction: 6,
    }).start();
  }, [currentMood]);

  if (currentMood === undefined) {
    return <SkeletonLayout showGrid />;
  }

  const getMoodIcon = (mood: string, size = 48) => {
    switch (mood) {
      case 'happy':
        return <Smile size={size} color="#22c55e" />;
      case 'sad':
        return <Frown size={size} color="#ef4444" />;
      case 'anxious':
        return <Cloud size={size} color="#f97316" />;
      case 'angry':
        return <Zap size={size} color="#dc2626" />;
      case 'excited':
        return <Heart size={size} color="#ec4899" />;
      case 'tired':
        return <Moon size={size} color="#6b7280" />;
      case 'stressed':
        return <Cloud size={size} color="#ef4444" />;
      case 'calm':
        return <Leaf size={size} color="#10b981" />;
      case 'neutral':
      case 'other':
      default:
        return <Meh size={size} color="#a8a29e" />;
    }
  };

  return (
    <Card className="overflow-hidden border-0  shadow-lg shadow-indigo-400/30">
      <LinearGradient
        className="p-5"
        end={{ x: 0.8, y: 0.8 }}
        start={{ x: 0.2, y: 0.2 }}
        colors={['#667eea', '#764ba2']}
        locations={[0, 1]}
      >
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center gap-4">
            <View className="bg-white p-3 rounded-xl">
              {currentMood ? (
                getMoodIcon(currentMood.mood, 28)
              ) : (
                <Meh size={28} color="gray" />
              )}
            </View>
            <View>
              <Text className="text-xl font-bold tracking-tight text-white">
                {currentMood ? `Feeling ${currentMood.mood}` : 'How are you?'}
              </Text>
              <Text className="text-sm text-indigo-200 mt-1">
                {currentMood
                  ? `Logged ${moment(currentMood._creationTime).fromNow()}`
                  : 'Log your mood to get started'}
              </Text>
            </View>
          </View>
          <TouchableOpacity
            onPress={onLogMoodPress}
            className="bg-white/20 p-2.5 rounded-full active:bg-white/30"
          >
            <SmilePlus size={22} color={'white'} />
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </Card>
  );
};

export default MoodSummary;
