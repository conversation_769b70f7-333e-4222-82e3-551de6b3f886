export interface PlanAdvice {
  text: string;
  icon: string; // Emoji or icon name
  color: string; // Hex or Tailwind color
}

export interface PlanAdvicesAndActions {
  advices: PlanAdvice[];
  recommendedActions: PlanAdvice[];
}

export const planAdvices: Record<string, PlanAdvicesAndActions> = {
  weight: {
    advices: [
      { text: 'Track your meals daily', icon: '📝', color: '#34d399' },
      { text: 'Stay hydrated', icon: '💧', color: '#60a5fa' },
      {
        text: 'Aim for gradual, sustainable weight loss',
        icon: '📉',
        color: '#fbbf24',
      },
    ],
    recommendedActions: [
      {
        text: 'Exercise at least 3 times a week',
        icon: '🏃‍♂️',
        color: '#f59e42',
      },
      { text: 'Prioritize whole foods', icon: '🥗', color: '#10b981' },
    ],
  },
  heart: {
    advices: [
      { text: 'Limit salt and processed foods', icon: '🧂', color: '#f87171' },
      { text: 'Eat more fruits and vegetables', icon: '🍎', color: '#34d399' },
    ],
    recommendedActions: [
      {
        text: 'Engage in regular aerobic exercise',
        icon: '🚴‍♂️',
        color: '#60a5fa',
      },
      { text: 'Monitor blood pressure', icon: '🩺', color: '#fbbf24' },
    ],
  },
  keto: {
    advices: [
      {
        text: 'Limit carbs, focus on healthy fats',
        icon: '🥑',
        color: '#10b981',
      },
      { text: 'Monitor ketone levels', icon: '🧪', color: '#6366f1' },
    ],
    recommendedActions: [
      { text: 'Plan meals ahead', icon: '📅', color: '#f59e42' },
      { text: 'Stay hydrated', icon: '💧', color: '#60a5fa' },
    ],
  },
  // Add more plans as needed
};
