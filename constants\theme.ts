export const theme = {
  colors: {
    primary: "#9B6BDF",
    primaryLight: "#F0F0F5",
    secondary: "#212121",
    accent: "#E0BBE4",
    success: "#8BC34A",
    warning: "#FFD740",
    error: "#EF5350",
    white: "#FFFFFF",
    black: "#212121",
    gray: {
      50: "#F9F9F9",
      100: "#F0F0F0",
      200: "#E0E0E0",
      300: "#C0C0C0",
      400: "#A0A0A0",
      500: "#808080",
      600: "#606060",
      700: "#404040",
      800: "#303030",
      900: "#202020",
    },
  },
  spacing: {
    xs: 4,
    s: 8,
    m: 12,
    l: 16,
    xl: 24,
    xxl: 32,
  },
  borderRadius: {
    s: 4,
    m: 8,
    l: 16,
    xl: 24,
    round: 9999,
  },
  typography: {
    fontFamily: {
      regular: "Poppins-Regular",
      medium: "Poppins-Medium",
      semiBold: "Poppins-SemiBold",
      bold: "Poppins-Bold",
    },
    fontSize: {
      xs: 11,
      s: 13,
      m: 14,
      l: 17,
      xl: 20,
      xxl: 24,
      xxxl: 32,
    },
  },
  shadows: {
    small: {
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.05,
      shadowRadius: 3,
      elevation: 2,
    },
    medium: {
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.08,
      shadowRadius: 6,
      elevation: 4,
    },
    large: {
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.12,
      shadowRadius: 10,
      elevation: 8,
    },
  },
  buttons: {
    primary: {
      backgroundColor: "#9B6BDF",
      textColor: "#FFFFFF",
    },
    secondary: {
      backgroundColor: "#212121",
      textColor: "#FFFFFF",
    },
    outline: {
      backgroundColor: "transparent",
      textColor: "#9B6BDF",
      borderColor: "#9B6BDF",
      borderWidth: 1,
    },
    success: {
      backgroundColor: "#8BC34A",
      textColor: "#FFFFFF",
    },
    error: {
      backgroundColor: "#EF5350",
      textColor: "#FFFFFF",
    },
  },
  text: {
    h1: {
      fontSize: 32,
      color: "#212121",
      fontFamily: "Poppins-Bold",
    },
    h2: {
      fontSize: 24,
      color: "#212121",
      fontFamily: "Poppins-SemiBold",
    },
    h3: {
      fontSize: 20,
      color: "#212121",
      fontFamily: "Poppins-Medium",
    },
    body: {
      fontSize: 15,
      color: "#212121",
      fontFamily: "Poppins-Regular",
    },
    small: {
      fontSize: 13,
      color: "#606060",
      fontFamily: "Poppins-Regular",
    },
    xs: {
      fontSize: 11,
      color: "#808080",
      fontFamily: "Poppins-Regular",
    },
    primary: {
      color: "#9B6BDF",
    },
    secondary: {
      color: "#212121",
    },
    success: {
      color: "#8BC34A",
    },
    error: {
      color: "#EF5350",
    },
    white: {
      color: "#FFFFFF",
    },
    black: {
      color: "#212121",
    },
    gray: {
      color: "#606060",
    },
  },
};
