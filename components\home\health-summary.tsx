import { StyleSheet, View } from 'react-native';
import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { <PERSON>, CardHeader, CardContent, CardFooter } from '../ui/card';
import { Text } from '../ui/text';
import { Button } from '../ui/button';
import {
  HeartPlus,
  Coffee,
  Utensils,
  Moon,
  AlertCircle,
  ForkKnife,
} from 'lucide-react-native';
import { Skeleton, SkeletonLayout } from '../ui/Skeleton';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { formatDate } from '@/lib/utils';
import { router } from 'expo-router';

const HealthSummary = () => {
  const healthPlan = useQuery(api.healthPlans.getActiveHealthPlan);
  const dailyConsumedCalories = useQuery(api.meals.getTotalCaloriesToday);

  if (healthPlan === undefined || dailyConsumedCalories === undefined)
    return <SkeletonLayout showGrid />;

  if (healthPlan === null) {
    return (
      <Card className="flex flex-col items-center justify-center p-6">
        <HeartPlus color={'#6b7280'} size={48} />
        <Text className="font-bold text-lg mt-4">No Health Plan Found</Text>
        <Text className="text-sm text-muted-foreground text-center mt-2">
          It looks like you don't have an active health plan. Create one to
          track your daily calories and health goals!
        </Text>
        <Button
          onPress={() => router.push('/health/new-plan')}
          className="w-full mt-4"
          variant="default"
        >
          <Text>Create Health Plan</Text>
        </Button>
      </Card>
    );
  }

  return (
    <View>
      <CardHeader className="flex-row px-0 items-center justify-between">
        <View className="flex flex-row items-center gap-4">
          <Button size={'icon'} variant={'default'}>
            <HeartPlus color={'white'} size={18} />
          </Button>
          <View>
            <Text className="font-bold text-lg capitalize">
              {healthPlan.type} plan
            </Text>
            <Text className="text-sm text-muted-foreground">
              Current health plan
            </Text>
          </View>
        </View>
        <View className="items-end">
          <Text className="text-lg font-bold text-gray-800">
            {healthPlan.dailyCalories.breakfast +
              healthPlan.dailyCalories.lunch +
              healthPlan.dailyCalories.dinner +
              healthPlan.dailyCalories.snacks}
          </Text>
          <Text className="text-sm font-normal text-gray-500">
            Daily target kcal
          </Text>
        </View>
      </CardHeader>

      <CardContent className="px-0">
        <View className="flex-row items-center gap-4">
          {['breakfast', 'lunch', 'dinner'].map((mealType) => (
            <View
              key={mealType}
              // style={{
              //   backgroundColor:
              //     mealType === 'breakfast'
              //       ? '#D6EDF4'
              //       : mealType === 'lunch'
              //         ? '#FCD1C0'
              //         : '#DDEAC4',
              // }}
              className="p-4 shadow-sm drop-shadow-md border border-border rounded-md flex-1 flex-col items-center justify-center"
            >
              {mealType === 'breakfast' && (
                <Coffee color={'#007bff'} size={20} />
              )}
              {mealType === 'lunch' && <Utensils color={'#e67e22'} size={24} />}
              {mealType === 'dinner' && <Moon color={'#27ae60'} size={24} />}
              {mealType === 'snacks' && <Moon color={'#27ae60'} size={24} />}
              <Text className="text-sm font-semibold mt-2 capitalize">
                {mealType}
              </Text>
              <Text className="text-lg font-bold text-gray-600">
                {
                  dailyConsumedCalories[
                    mealType as keyof typeof dailyConsumedCalories
                  ]
                }{' '}
                /{' '}
                {
                  healthPlan.dailyCalories[
                    mealType as keyof typeof healthPlan.dailyCalories
                  ]
                }
              </Text>
            </View>
          ))}
        </View>

        {(() => {
          const exceededMeals: string[] = [];
          if (
            dailyConsumedCalories.breakfast > healthPlan.dailyCalories.breakfast
          ) {
            exceededMeals.push('Breakfast');
          }
          if (dailyConsumedCalories.lunch > healthPlan.dailyCalories.lunch) {
            exceededMeals.push('Lunch');
          }
          if (dailyConsumedCalories.dinner > healthPlan.dailyCalories.dinner) {
            exceededMeals.push('Dinner');
          }

          if (exceededMeals.length > 0) {
            return (
              <Alert icon={AlertCircle} variant="destructive" className="mt-4">
                <AlertTitle className="font-medium">
                  Calories Exceeded!
                </AlertTitle>
                <AlertDescription className="text-muted-foreground">
                  You consumed more calories than your target for{' '}
                  {exceededMeals.join(', and ')}.
                </AlertDescription>
              </Alert>
            );
          }
          return null;
        })()}
      </CardContent>
      <CardFooter className="px-0">
        <View className="flex-row w-full gap-4">
          <Button className="flex-1">
            <Text>Manage Plan</Text>
          </Button>
          <Button variant={'outline'} className="flex-1 flex-row gap-3">
            <ForkKnife size={16} />
            <Text>Add Meal</Text>
          </Button>
        </View>
      </CardFooter>
    </View>
  );
};

export default HealthSummary;

const styles = StyleSheet.create({});
